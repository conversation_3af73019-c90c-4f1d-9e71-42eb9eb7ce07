# Install required system dependencies
# FFmpeg: for video processing and frame extraction
# ImageMagick: for image manipulation and creating movie barcodes
%%capture
!apt update && apt install -y ffmpeg imagemagick

import seaborn as sns
import cv2
import numpy as np
import matplotlib.pyplot as plt
import zipfile
import os
import glob
from IPython.display import Image, display
from google.colab import files
from sklearn.cluster import KMeans

# Set to True to skip the upload step and use existing files in extracted_videos/
skip_upload = False #@param {type:"boolean"}

video_files = []

if not skip_upload:
    # Upload ZIP file containing MP4 videos
    print("Please upload a ZIP file containing your MP4 video files:")
    uploaded = files.upload()

    # Extract ZIP file and get list of MP4 files
    for filename in uploaded.keys():
        if filename.endswith('.zip'):
            print(f"Extracting {filename}...")
            with zipfile.ZipFile(filename, 'r') as zip_ref:
                zip_ref.extractall("extracted_videos/")

            # Find all MP4 files in the extracted directory
            video_files = glob.glob("extracted_videos/**/*.mp4", recursive=True)
            break

    # If no ZIP file was uploaded, look for direct MP4 uploads
    if not video_files:
        video_files = [f for f in uploaded.keys() if f.endswith('.mp4')]
else:
    print("Skipping upload step. Using existing video files in extracted_videos/")
    video_files = glob.glob("extracted_videos/**/*.mp4", recursive=True)

# Display found video files
print(f"\nFound {len(video_files)} video file(s):")
for i, video in enumerate(video_files, 1):
    print(f"{i}. {os.path.basename(video)}")

if not video_files:
    print("⚠️ No MP4 files found. Please upload a ZIP file containing MP4 videos or upload MP4 files directly.")

# Movie Barcode Configuration
width = 800 #@param {type: "integer"}
height = 140 #@param {type: "integer"}
step = 100 #@param {type: "integer"}

%%capture

# Generate normalized movie barcodes
print("Generating movie barcodes...")

for f in video_files:
  videofile = f
  imagefile = f + "_barcode.png"
  print(f"Processing: {os.path.basename(videofile)}")
  !ffmpeg -i "{videofile}" \
    -filter:v "select=not(mod(n\,{step})),setpts=N/(FRAME_RATE*TB),scale=1:1" \
    -f image2pipe -c:v ppm - | \
  convert \( +append - \) -resize "{width}x{height}!" "{imagefile}"

print("✅ Movie barcodes completed!")

for i, video_file in enumerate(video_files, 1):
    print(f"{i}. {os.path.basename(video_file)}")
    try:
        display(Image(filename=video_file+"_barcode.png"))
    except FileNotFoundError:
        print(f"⚠️ Barcode not found for {os.path.basename(video_file)}. Please ensure the previous step completed successfully.")
    print("-" * 50)

# Extract brightness data from all video files
brightness_sets = []
video_lengths_sets = []
warm_cool_sets = [] # New list to store warm-cool axis data

print("Extracting brightness and warm-cool axis data from video files...")

for i, file in enumerate(video_files):
    print(f"Processing {i+1}/{len(video_files)}: {os.path.basename(file)}")

    brightness = []
    video_lengths = []
    warm_cool = [] # List to store warm-cool axis for the current video

    # Open video file
    cap = cv2.VideoCapture(file)

    # Check if video opened successfully
    if not cap.isOpened():
        print(f"⚠️ Warning: Could not open video file {os.path.basename(file)}")
        brightness_sets.append([])
        video_lengths_sets.append([])
        warm_cool_sets.append([]) # Append empty list for warm-cool
        continue

    frame_count = 0

    # Extract brightness and warm-cool axis from each frame
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # Convert to grayscale for brightness calculation
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Calculate mean brightness (0-255 scale)
        frame_brightness = np.mean(gray)
        brightness.append(frame_brightness)

        # Convert to L*a*b* color space for warm-cool axis calculation
        lab = cv2.cvtColor(frame, cv2.COLOR_BGR2Lab)

        # The 'b*' channel in L*a*b* represents the yellow-blue axis
        # Positive values are yellow, negative values are blue
        frame_warm_cool = np.mean(lab[:,:,2]) # Mean of the 'b*' channel
        warm_cool.append(frame_warm_cool)


        # Track frame position for x-axis
        frame_count += 1
        video_lengths.append(frame_count)

        # Optional: Process every Nth frame for large videos (uncomment if needed)
        # for _ in range(4):  # Skip 4 frames to speed up processing
        #     cap.read()

    cap.release()

    # Store results
    brightness_sets.append(brightness)
    video_lengths_sets.append(video_lengths)
    warm_cool_sets.append(warm_cool) # Store warm-cool data

    print(f"  ✅ Extracted {len(brightness)} frames from {os.path.basename(file)}")

print(f"\n✅ Brightness and warm-cool axis extraction completed for {len(video_files)} videos!")

# Check if we have any valid data
valid_videos = sum(1 for brightness in brightness_sets if len(brightness) > 0)
print(f"Successfully processed {valid_videos} out of {len(video_files)} videos")

# Visualize brightness progression with smoothing
print("Generating brightness analysis plots...\n")

# Configuration for smoothing and display
window_size = 10  # Moving average window for smoothing
max_frames_display = 5000  # Limit display to first N frames for better visualization

for i, brightness in enumerate(brightness_sets):
    # Skip empty brightness data
    if len(brightness) == 0:
        print(f"⚠️ Skipping {os.path.basename(video_files[i])} - no brightness data available")
        continue

    # Limit the number of frames for visualization
    max_frames = min(len(brightness), max_frames_display)
    brightness_subset = brightness[:max_frames]
    video_lengths_subset = video_lengths_sets[i][:max_frames]

    # Apply smoothing using moving average (only if we have enough data points)
    if len(brightness_subset) >= window_size:
        brightness_smooth = np.convolve(
            brightness_subset,
            np.ones(window_size)/window_size,
            mode='same'
        )
    else:
        # If not enough data for smoothing, use original data
        brightness_smooth = brightness_subset
        print(f"⚠️ Note: {os.path.basename(video_files[i])} has only {len(brightness_subset)} frames - smoothing skipped")

    # Create visualization - Reduced height
    plt.figure(figsize=(12, 2)) # Reduced height to 2 inches

    # Plot raw brightness (light gray)
    plt.plot(video_lengths_subset, brightness_subset,
             color='lightgray', alpha=0.5, linewidth=0.5, label='Raw brightness')

    # Plot smoothed brightness (black)
    plt.plot(video_lengths_subset, brightness_smooth,
             color='black', linewidth=1.2, label=f'Smoothed brightness (window={window_size})')

    # Formatting
    plt.ylim(0, 255)
    plt.xlim(0, max(video_lengths_subset) if video_lengths_subset else 1)
    plt.xlabel('Frame Number')
    plt.ylabel('Brightness (0-255)')
    plt.title(f'Brightness Analysis: {os.path.basename(video_files[i])}', fontsize=10, fontweight='bold') # Smaller title font
    plt.legend(fontsize=8) # Smaller legend font
    plt.grid(True, alpha=0.3)

    # Add statistics annotation - Smaller font
    if len(brightness_smooth) > 0:
        mean_brightness = np.mean(brightness_smooth)
        std_brightness = np.std(brightness_smooth)
        min_brightness = np.min(brightness_smooth)
        max_brightness = np.max(brightness_smooth)

        stats_text = f'Mean: {mean_brightness:.1f} | Std: {std_brightness:.1f} | Range: {min_brightness:.1f}-{max_brightness:.1f}'
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8), fontsize=8) # Smaller annotation font

    plt.tight_layout()
    plt.show()

    # Print summary
    print(f"✅ Analyzed {len(brightness_subset)} frames from {os.path.basename(video_files[i])}")
    if len(brightness_subset) < len(brightness):
        print(f"   (Showing first {max_frames_display} of {len(brightness)} total frames)")
    print("-" * 60)

print("✅ Brightness analysis visualization completed!")

# Visualize warm-cool axis progression with smoothing
print("Generating warm-cool axis analysis plots...\n")

# Configuration for smoothing and display
window_size_color = 10  # Moving average window for smoothing

for i, warm_cool_data in enumerate(warm_cool_sets):
    # Skip empty warm-cool data
    if len(warm_cool_data) == 0:
        print(f"⚠️ Skipping {os.path.basename(video_files[i])} - no warm-cool data available")
        continue

    # Limit the number of frames for visualization (optional, uncomment if needed)
    # max_frames = min(len(warm_cool_data), max_frames_display)
    # warm_cool_subset = warm_cool_data[:max_frames]
    # video_lengths_subset = video_lengths_sets[i][:max_frames]
    warm_cool_subset = warm_cool_data
    video_lengths_subset = video_lengths_sets[i]


    # Apply smoothing using moving average (only if we have enough data points)
    if len(warm_cool_subset) >= window_size_color:
        warm_cool_smooth = np.convolve(
            warm_cool_subset,
            np.ones(window_size_color)/window_size_color,
            mode='same'
        )
    else:
        # If not enough data for smoothing, use original data
        warm_cool_smooth = warm_cool_subset
        print(f"⚠️ Note: {os.path.basename(video_files[i])} has only {len(warm_cool_subset)} frames - smoothing skipped")

    # Create visualization - Reduced height
    plt.figure(figsize=(12, 2)) # Reduced height to 2 inches


    # Plot smoothed warm-cool (colored based on value)
    # Using a colormap (e.g., coolwarm) to indicate warm/cool
    plt.plot(video_lengths_subset, warm_cool_smooth,
             color='black', linewidth=1.2) # Removed label


    # Add a horizontal line at the neutral point (typically 128 for the b* channel)
    plt.axhline(y=128, color='gray', linestyle='--', linewidth=0.8, label='Neutral (b*=128)')


    # Formatting
    # The 'b*' channel in L*a*b* typically ranges from ~0 to ~255, with 128 being neutral
    plt.ylim(0, 255)
    plt.xlim(0, max(video_lengths_subset) if video_lengths_subset else 1)
    plt.xlabel('Frame Number')
    plt.ylabel('Warm-Cool Axis (b* in L*a*b*)')
    plt.title(f'Warm-Cool Axis Analysis: {os.path.basename(video_files[i])}', fontsize=10, fontweight='bold') # Smaller title font
    # plt.legend(fontsize=8) # Removed legend
    plt.grid(True, alpha=0.3)

    # Add statistics annotation - Smaller font
    if len(warm_cool_smooth) > 0:
        mean_warm_cool = np.mean(warm_cool_smooth)
        std_warm_cool = np.std(warm_cool_smooth)
        min_warm_cool = np.min(warm_cool_smooth)
        max_warm_cool = np.max(warm_cool_smooth)

        stats_text = f'Mean: {mean_warm_cool:.1f} | Std: {std_warm_cool:.1f} | Range: {min_warm_cool:.1f}-{max_warm_cool:.1f}'
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8), fontsize=8) # Smaller annotation font


    plt.tight_layout()
    plt.show()

    # Print summary
    print(f"✅ Analyzed {len(warm_cool_subset)} frames from {os.path.basename(video_files[i])}")
    # if 'max_frames' in locals() and len(warm_cool_subset) < len(warm_cool_data):
    #     print(f"   (Showing first {max_frames_display} of {len(warm_cool_data)} total frames)")
    print("-" * 60)

print("✅ Warm-Cool Axis analysis visualization completed!")

# # Define reference colors for analysis (in RGB format)
# # These represent common color categories used in film analysis
# reference_colors = {
#     'Red': [255, 0, 0],
#     'Orange': [255, 165, 0],
#     'Yellow': [255, 255, 0],
#     'Green': [0, 255, 0],
#     'Blue': [0, 0, 255],
#     'Purple': [128, 0, 128],
#     'Pink': [255, 192, 203],
#     'Brown': [165, 42, 42],
#     'Gray': [128, 128, 128],
#     'Cyan': [0, 255, 255]
# }

# def find_closest_reference_color(rgb_color, reference_colors):
#     """Find the closest reference color to a given RGB color"""
#     min_distance = float('inf')
#     closest_color = None

#     for color_name, color_rgb in reference_colors.items():
#         # Calculate Euclidean distance in RGB space
#         distance = np.sqrt(sum((rgb_color[i] - color_rgb[i])**2 for i in range(3)))
#         if distance < min_distance:
#             min_distance = distance
#             closest_color = color_name

#     return closest_color

# def get_dominant_color(frame, num_colors=1):
#     """Extract the dominant color(s) from a frame using k-means clustering"""
#     from sklearn.cluster import KMeans

#     # Reshape frame to be a list of pixels
#     pixels = frame.reshape(-1, 3)

#     # Use k-means to find dominant colors
#     kmeans = KMeans(n_clusters=num_colors, random_state=42, n_init=10)
#     kmeans.fit(pixels)

#     # Get the most dominant color (center of largest cluster)
#     labels = kmeans.labels_
#     dominant_color = kmeans.cluster_centers_[0]  # Take first cluster center

#     return dominant_color.astype(int)

# print("✅ Reference colors and utility functions defined!")
# print(f"Reference colors: {', '.join(reference_colors.keys())}")

# import skimage.color

# def rgb_to_cieluv(rgb):
#     # Convert RGB [0,255] to [0,1] then to CIELUV
#     rgb_norm = np.array(rgb) / 255.0
#     return skimage.color.rgb2luv(rgb_norm.reshape(1, 1, 3)).reshape(3)

# # Define CIELUV bins (e.g., 264 bins)
# bin_count = 264

# # Generate reference colors in CIELUV
# reference_colors_cieluv = {}
# for name, rgb in reference_colors.items():
#     reference_colors_cieluv[name] = rgb_to_cieluv(rgb)

# # Helper: assign CIELUV bin
# luv_min, luv_max = 0, 100  # L range, U/V can be wider
# luv_bins = np.linspace(luv_min, luv_max, bin_count+1)
# def assign_luv_bin(luv):
#     # Only use L for binning (for simplicity)
#     L = luv[0]
#     bin_idx = np.digitize(L, luv_bins) - 1
#     return min(max(bin_idx, 0), bin_count-1)

# # MPH and MDH calculation
# Th = 0.2  # Threshold (can be tuned)

# mph_histograms = {}
# mdh_histograms = {}

# for i, video_file in enumerate(video_files):
#     print(f"Processing video {i+1}/{len(video_files)}: {os.path.basename(video_file)}")
#     cap = cv2.VideoCapture(video_file)
#     shot_luv_bins = []
#     shot_rdcs = []
#     shot_ref_color = []
#     frame_step = 60  # treat every 60th frame as a shot
#     frame_count = 0
#     processed_shots = 0
#     while True:
#         ret, frame = cap.read()
#         if not ret:
#             break
#         frame_count += 1
#         if frame_count % frame_step == 0:
#             frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
#             pixels = frame_rgb.reshape(-1, 3)
#             luv_pixels = skimage.color.rgb2luv(pixels / 255.0)
#             # Compute histogram Hk(i)
#             bins = [assign_luv_bin(luv) for luv in luv_pixels]
#             Hk = np.bincount(bins, minlength=bin_count)
#             # Find top 3 dominant bins
#             sorted_bins = np.argsort(Hk)[::-1]
#             P1, P2, P3 = sorted_bins[:3]
#             D1 = Hk[P1]
#             D2 = Hk[P2] if Hk[P2] > D1 * Th else 0
#             D3 = Hk[P3] if Hk[P3] > D1 * Th else 0
#             # RDCS(k)
#             total_D = D1 + D2 + D3
#             W1 = D1 / total_D if total_D else 0
#             W2 = D2 / total_D if total_D else 0
#             W3 = D3 / total_D if total_D else 0
#             rdcs = P1 * W1 + P2 * W2 + P3 * W3
#             shot_luv_bins.append((P1, P2, P3))
#             shot_rdcs.append(rdcs)
#             # Assign closest reference color
#             # Use bin center to get LUV value
#             L_bin = (luv_bins[P1] + luv_bins[P1+1]) / 2
#             # Find closest reference color
#             min_dist = float('inf')
#             closest_ref = None
#             for name, ref_luv in reference_colors_cieluv.items():
#                 dist = np.linalg.norm([L_bin-ref_luv[0]])  # Only L for simplicity
#                 if dist < min_dist:
#                     min_dist = dist
#                     closest_ref = name
#             shot_ref_color.append(closest_ref)
#             processed_shots += 1
#             if processed_shots % 30 == 0:
#                 print(f"  Processed {processed_shots} shots...")

#     cap.release()
#     # MPH: histogram of reference color assignments
#     mph = {name: 0 for name in reference_colors.keys()}
#     for ref in shot_ref_color:
#         mph[ref] += 1
#     mph_histograms[os.path.basename(video_file)] = mph
#     # MDH: transitions between reference colors
#     mdh = {f'{c1}->{c2}': 0 for c1 in reference_colors.keys() for c2 in reference_colors.keys()}
#     for i in range(1, len(shot_ref_color)):
#         c_prev, c_curr = shot_ref_color[i-1], shot_ref_color[i]
#         key = f'{c_prev}->{c_curr}'
#         mdh[key] += 1
#     mdh_histograms[os.path.basename(video_file)] = mdh
#     print(f"  ✅ Completed: {processed_shots} shots analyzed for {os.path.basename(video_file)}")

# print('✅ MPH and MDH calculation (method-based) completed!')
# # Example: print results for first video
# first_video = os.path.basename(video_files[0]) if video_files else None
# if first_video:
#     print('MPH:', mph_histograms[first_video])
#     print('MDH:', {k:v for k,v in mdh_histograms[first_video].items() if v>0})

# # Visualize Movie Palette Histograms (MPH) - Updated for method-based implementation
# print("Generating Movie Palette Histogram visualizations...\\n")

# fig, axes = plt.subplots(len(video_files), 1, figsize=(12, 4*len(video_files)))
# if len(video_files) == 1:
#     axes = [axes]

# for i, video_file in enumerate(video_files):
#     video_name = os.path.basename(video_file)
#     color_freq = mph_histograms[video_name]
#     colors = list(color_freq.keys())
#     frequencies = list(color_freq.values())

#     # Create color map for bars
#     bar_colors = [np.array(reference_colors[color])/255.0 for color in colors]

#     bars = axes[i].bar(colors, frequencies, color=bar_colors, edgecolor='black', linewidth=0.5)
#     axes[i].set_title(f'Movie Palette Histogram (Method-based): {video_name}', fontsize=12, fontweight='bold')
#     axes[i].set_xlabel('Reference Colors')
#     axes[i].set_ylabel('Frequency (Shot Count)')
#     axes[i].tick_params(axis='x', rotation=45)

#     # Add frequency values on top of bars
#     for bar, freq in zip(bars, frequencies):
#         if freq > 0:
#             axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
#                         str(freq), ha='center', va='bottom', fontsize=8)

# plt.tight_layout()
# plt.show()

# print("✅ Movie Palette Histogram visualizations completed!")

# # Visualize Mood Dynamics Histograms (MDH) - Updated for method-based implementation
# print("\\nGenerating Mood Dynamics Histogram visualizations...\\n")

# for video_file in video_files:
#     video_name = os.path.basename(video_file)
#     mdh = mdh_histograms[video_name]

#     # Convert transition dict to matrix for heatmap
#     color_names = list(reference_colors.keys())
#     transition_matrix = np.zeros((len(color_names), len(color_names)))

#     for i, c1 in enumerate(color_names):
#         for j, c2 in enumerate(color_names):
#             key = f'{c1}->{c2}'
#             transition_matrix[i, j] = mdh.get(key, 0)

#     plt.figure(figsize=(10, 8))

#     # Create heatmap of transitions
#     sns.heatmap(transition_matrix,
#                 xticklabels=color_names,
#                 yticklabels=color_names,
#                 annot=True,
#                 fmt='.0f',
#                 cmap='Blues',
#                 cbar_kws={'label': 'Transition Frequency'})

#     plt.title(f'Mood Dynamics Histogram (Method-based): {video_name}', fontsize=14, fontweight='bold')
#     plt.xlabel('To Color')
#     plt.ylabel('From Color')
#     plt.xticks(rotation=45)
#     plt.yticks(rotation=0)

#     plt.tight_layout()
#     plt.show()

#     # Summary statistics
#     total_transitions = np.sum(transition_matrix)
#     if total_transitions > 0:
#         print(f"Total color transitions in {video_name}: {int(total_transitions)}")

#         # Find most common transitions
#         max_transition = np.max(transition_matrix)
#         if max_transition > 0:
#             max_pos = np.unravel_index(np.argmax(transition_matrix), transition_matrix.shape)
#             from_color = color_names[max_pos[0]]
#             to_color = color_names[max_pos[1]]
#             print(f"Most frequent transition: {from_color} → {to_color} ({int(max_transition)} times)")
#     print("-" * 50)

# print("✅ Mood Dynamics Histogram visualizations completed!")